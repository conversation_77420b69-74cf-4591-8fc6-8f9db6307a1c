<?php

namespace App\Http\Controllers\Paciente;

use App\Http\Controllers\Controller;
use App\Models\Plano;
use App\Models\Assinatura;
use App\Services\AffiliateTrackingService;
use App\Services\MercadoPagoService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;

class PlanosController extends Controller
{
    /**
     * Exibe a página de seleção de planos
     */
    public function index()
    {
        $planos = [
            [
                'id' => 'plano-busca',
                'name' => 'Plano Busca',
                'price' => 14.80,
                'description' => 'Encontre fisioterapeutas, farmácias e dentistas próximos a você',
                'features' => [
                    'Busca ilimitada de profissionais',
                    'Filtros avançados por especialidade',
                    'Visualização de avaliações',
                    'Informações de contato',
                    'Localização no mapa',
                    'Horários de funcionamento',
                    'Suporte por chat',
                ],
                'type' => 'busca'
            ],
            [
                'id' => 'plano-pessoal',
                'name' => 'Plano Pessoal',
                'price' => 180.00,
                'description' => 'Atendimento fisioterapêutico domiciliar personalizado',
                'features' => [
                    'Todos os recursos do Plano Busca',
                    'Agendamento de consultas domiciliares',
                    'Fisioterapeutas qualificados',
                    'Atendimento personalizado',
                    'Relatórios de avaliação',
                    'Suporte telefônico',
                    'Cancelamento flexível',
                    'Histórico médico digital',
                ],
                'type' => 'pessoal',
                'popular' => true
            ],
            [
                'id' => 'plano-empresarial',
                'name' => 'Plano Empresarial',
                'price' => 640.00,
                'description' => 'Solução completa de fisioterapia para sua empresa',
                'features' => [
                    'Todos os recursos do Plano Pessoal',
                    'Atendimento para até 20 funcionários',
                    'Gestão centralizada',
                    'Relatórios empresariais',
                    'Programa de prevenção',
                    'Treinamentos ergonômicos',
                    'Descontos em consultas extras',
                    'Dashboard administrativo',
                ],
                'type' => 'empresarial'
            ]
        ];

        $user = auth()->user();

        // Verificar se o usuário já tem um plano selecionado
        $planoAtual = null;
        if ($user->has_subscription) {
            $assinatura = \App\Models\Assinatura::where('user_id', $user->id)
                ->where('status', 'ativa')
                ->first();

            if ($assinatura) {
                // Determinar o tipo de plano baseado no preço
                $planoType = 'pessoal'; // default
                if ($assinatura->monthly_price == 14.80) {
                    $planoType = 'busca';
                    $planoName = 'Plano Busca';
                } elseif ($assinatura->monthly_price == 180.00) {
                    $planoType = 'pessoal';
                    $planoName = 'Plano Pessoal';
                } elseif ($assinatura->monthly_price == 640.00) {
                    $planoType = 'empresarial';
                    $planoName = 'Plano Empresarial';
                }

                $planoAtual = [
                    'type' => $planoType,
                    'name' => $planoName,
                    'status' => $assinatura->status
                ];
            }
        }

        return Inertia::render('paciente/planos', [
            'planos' => $planos,
            'planoAtual' => $planoAtual,
            'hasSubscription' => $user->has_subscription
        ]);
    }

    /**
     * Processa a seleção do plano
     */
    public function store(Request $request)
    {
        \Log::info('PlanosController@store called', [
            'user' => auth()->user(),
            'request_data' => $request->all()
        ]);

        $request->validate([
            'plano_type' => 'required|string|in:busca,pessoal,empresarial'
        ]);

        $user = auth()->user();
        $planoType = $request->plano_type;

        \Log::info('Processing plan selection', [
            'user_id' => $user->id,
            'plano_type' => $planoType,
            'current_has_subscription' => $user->has_subscription
        ]);

        // Se for plano busca, processar diretamente (gratuito)
        if ($planoType === 'busca') {
            return $this->processBuscaPlan($user);
        }

        // Se for plano pessoal ou empresarial, criar preferência de pagamento
        if ($planoType === 'pessoal') {
            return $this->processPessoalPlan($user, $request);
        }

        if ($planoType === 'empresarial') {
            return $this->processEmpresarialPlan($user, $request);
        }

        return back()->with('error', 'Tipo de plano inválido.');
    }

    /**
     * Processa plano busca (gratuito)
     */
    private function processBuscaPlan($user)
    {
        // Se já tem assinatura, cancelar primeiro
        if ($user->has_subscription) {
            $assinaturaAtiva = \App\Models\Assinatura::where('user_id', $user->id)
                ->where('status', 'ativa')
                ->first();

            if ($assinaturaAtiva) {
                $assinaturaAtiva->update(['status' => 'cancelada']);
                \Log::info('Cancelled previous subscription for plan change', [
                    'user_id' => $user->id,
                    'old_subscription_id' => $assinaturaAtiva->id
                ]);
            }
        }

        // Buscar ou criar um plano busca
        $plano = \App\Models\Plano::firstOrCreate(
            ['name' => 'Plano Busca'],
            [
                'name' => 'Plano Busca',
                'description' => 'Busca ilimitada de profissionais de fisioterapia',
                'price' => 14.80,
                'sessions_per_month' => 0,
                'active' => true,
            ]
        );

        // Criar assinatura gratuita para plano busca
        $assinatura = \App\Models\Assinatura::create([
            'user_id' => $user->id,
            'plano_id' => $plano->id,
            'status' => 'ativa',
            'start_date' => \Carbon\Carbon::now(),
            'end_date' => \Carbon\Carbon::now()->addMonth(),
            'sessions_used' => 0,
            'current_period_start' => \Carbon\Carbon::now(),
            'current_period_end' => \Carbon\Carbon::now()->addMonth(),
            'monthly_price' => 14.80,
        ]);

        // Marcar como tendo assinatura ativa
        $user->update([
            'plan_selected' => true,
            'plan_selected_at' => now(),
            'checkout_completed' => true,
            'checkout_completed_at' => now(),
            'has_subscription' => true,
        ]);

        \Log::info('Plano Busca ativado', [
            'user_id' => $user->id,
            'assinatura_id' => $assinatura->id
        ]);

        return redirect()->route('paciente.dashboard')
            ->with('success', 'Plano Busca ativado com sucesso!');
    }

    /**
     * Processa plano pessoal (pago)
     */
    private function processPessoalPlan($user, $request)
    {
        $monthlyPrice = 180.00;

        // Cancelar assinatura anterior se existir
        if ($user->has_subscription) {
            $assinaturaAtiva = \App\Models\Assinatura::where('user_id', $user->id)
                ->where('status', 'ativa')
                ->first();

            if ($assinaturaAtiva) {
                $assinaturaAtiva->update(['status' => 'cancelada']);
                \Log::info('Cancelled previous subscription for plan change', [
                    'user_id' => $user->id,
                    'old_subscription_id' => $assinaturaAtiva->id
                ]);
            }
        }

        // Buscar ou criar um plano pessoal
        $plano = \App\Models\Plano::firstOrCreate(
            ['name' => 'Plano Pessoal'],
            [
                'name' => 'Plano Pessoal',
                'description' => 'Atendimento fisioterapêutico domiciliar personalizado',
                'price' => $monthlyPrice,
                'sessions_per_month' => 1,
                'active' => true,
            ]
        );

        // Criar assinatura
        $assinatura = Assinatura::create([
            'user_id' => $user->id,
            'plano_id' => $plano->id,
            'status' => 'suspensa', // Suspensa até confirmação do pagamento
            'start_date' => Carbon::now(),
            'end_date' => Carbon::now()->addMonth(),
            'sessions_used' => 0,
            'current_period_start' => Carbon::now(),
            'current_period_end' => Carbon::now()->addMonth(),
            'monthly_price' => $monthlyPrice,
        ]);

        // Criar primeiro pagamento
        $pagamento = \App\Models\Pagamento::create([
            'assinatura_id' => $assinatura->id,
            'amount' => $monthlyPrice,
            'status' => 'pendente',
            'due_date' => Carbon::now()->addDays(7),
            'method' => 'cartao_credito',
            'notes' => "Pagamento mensal - Plano Pessoal",
            'gateway_response' => [
                'plano_type' => 'pessoal',
                'first_payment' => true,
            ]
        ]);

        // Processar venda de afiliado se houver referência
        $affiliateService = new AffiliateTrackingService();
        $affiliateService->createAffiliateSale(
            $user,
            $assinatura->id,
            'pessoal',
            $monthlyPrice,
            $request
        );

        // Marcar plano como selecionado
        $user->update([
            'plan_selected' => true,
            'plan_selected_at' => now(),
        ]);

        // Se estiver em modo dev, simular pagamento e redirecionar
        if (config('app.env') === 'local' || config('services.mercadopago.simulate', false)) {
            return $this->handleDevModePayment($user, $assinatura, $pagamento);
        }

        // Em produção, criar preferência do Mercado Pago
        return $this->createMercadoPagoPreference($user, $assinatura, $pagamento);
    }

    /**
     * Processa plano empresarial
     */
    private function processEmpresarialPlan($user, $request)
    {
        // Redirecionar para WhatsApp para planos empresariais
        $message = urlencode(
            'Olá! Gostaria de saber mais sobre o plano empresarial de fisioterapia domiciliar para grupos/empresas. Meu nome é ' . $user->name . ' e meu email é ' . $user->email . '.'
        );

        return redirect()->away("https://wa.me/5511978196207?text={$message}");
    }

    /**
     * Lidar com pagamento em modo dev
     */
    private function handleDevModePayment($user, $assinatura, $pagamento)
    {
        // Em modo dev, simular pagamento aprovado automaticamente
        $assinatura->update([
            'status' => 'ativa',
        ]);

        $pagamento->update([
            'status' => 'pago',
            'paid_at' => now(),
            'transaction_id' => 'DEV_' . uniqid(),
            'gateway_response' => [
                'status' => 'approved',
                'payment_id' => 'DEV_' . uniqid(),
                'mode' => 'development'
            ]
        ]);

        $user->update([
            'checkout_completed' => true,
            'checkout_completed_at' => now(),
            'has_subscription' => true,
        ]);

        \Log::info('Dev mode payment simulated', [
            'user_id' => $user->id,
            'assinatura_id' => $assinatura->id,
            'pagamento_id' => $pagamento->id
        ]);

        // Abrir simulador em nova aba (apenas em dev)
        $simulatorUrl = route('mercadopago.simulator.payment', ['transaction_id' => $pagamento->transaction_id]);

        // Em modo dev, redirecionar para dashboard com dados do simulador
        return redirect()->route('paciente.dashboard')
            ->with('success', 'Plano Pessoal ativado com sucesso! (Modo Desenvolvimento)')
            ->with('simulator_url', $simulatorUrl)
            ->with('open_simulator', true);
    }

    /**
     * Criar preferência do Mercado Pago
     */
    private function createMercadoPagoPreference($user, $assinatura, $pagamento)
    {
        try {
            $mercadoPagoService = new MercadoPagoService();

            $preferenceData = [
                'title' => 'Plano Pessoal - F4 Fisio',
                'description' => 'Assinatura mensal do Plano Pessoal de Fisioterapia Domiciliar',
                'amount' => $pagamento->amount,
                'payer' => [
                    'name' => $user->name,
                    'email' => $user->email,
                ],
                'external_reference' => $pagamento->id,
                'success_url' => route('paciente.pagamentos.success'),
                'failure_url' => route('paciente.pagamentos.failure'),
                'pending_url' => route('paciente.pagamentos.pending'),
                'notification_url' => route('mercadopago.webhook'),
            ];

            $preference = $mercadoPagoService->createPreference($preferenceData);

            if (!$preference['success']) {
                \Log::error('Erro ao criar preferência Mercado Pago', [
                    'user_id' => $user->id,
                    'pagamento_id' => $pagamento->id,
                    'error' => $preference['message'] ?? 'Erro desconhecido'
                ]);

                return back()->with('error', 'Erro ao processar pagamento. Tente novamente.');
            }

            // Atualizar pagamento com dados da preferência
            $pagamento->update([
                'transaction_id' => $preference['preference_id'],
                'gateway_response' => $preference,
            ]);

            // Redirecionar para o checkout do Mercado Pago
            $checkoutUrl = $preference['sandbox_init_point'] ?? $preference['init_point'];

            \Log::info('Mercado Pago preference created', [
                'user_id' => $user->id,
                'pagamento_id' => $pagamento->id,
                'preference_id' => $preference['preference_id'],
                'checkout_url' => $checkoutUrl
            ]);

            return redirect($checkoutUrl);

        } catch (\Exception $e) {
            \Log::error('Erro ao criar preferência Mercado Pago', [
                'user_id' => $user->id,
                'pagamento_id' => $pagamento->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return back()->with('error', 'Erro interno. Tente novamente ou entre em contato com o suporte.');
        }
    }

    /**
     * Retorna dados do plano baseado no tipo
     */
    private function getPlanoData($type)
    {
        $planos = [
            'busca' => ['price' => 14.80, 'sessions' => 0],
            'pessoal' => ['price' => 180.00, 'sessions' => 1],
            'empresarial' => ['price' => 640.00, 'sessions' => 4],
        ];

        return $planos[$type] ?? ['price' => 0, 'sessions' => 0];
    }

    /**
     * Verificar se o Mercado Pago está configurado
     */
    public function checkMercadoPagoStatus()
    {
        $mercadoPagoService = new MercadoPagoService();

        // Verificar se está em modo simulação ou se tem credenciais configuradas
        $isConfigured = config('services.mercadopago.access_token') ||
                       config('services.mercadopago.simulate', false) ||
                       config('app.env') === 'local';

        return response()->json([
            'configured' => $isConfigured,
            'sandbox_mode' => config('services.mercadopago.sandbox', true),
            'simulation_mode' => config('services.mercadopago.simulate', false) || config('app.env') === 'local'
        ]);
    }
}
